{"version": 3, "sources": [], "sections": [{"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/xampp1/htdocs/WIIZE%20SAB/camp-management-app/src/app/auth/register/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport Link from 'next/link'\nimport { useRouter } from 'next/navigation'\nimport { Eye, EyeOff, Mail, Lock, User, Phone } from 'lucide-react'\n\nexport default function RegisterPage() {\n  const [formData, setFormData] = useState({\n    firstName: '',\n    lastName: '',\n    email: '',\n    phone: '',\n    password: '',\n    confirmPassword: ''\n  })\n  const [showPassword, setShowPassword] = useState(false)\n  const [showConfirmPassword, setShowConfirmPassword] = useState(false)\n  const [isLoading, setIsLoading] = useState(false)\n  const [error, setError] = useState('')\n  const router = useRouter()\n\n  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {\n    setFormData({\n      ...formData,\n      [e.target.name]: e.target.value\n    })\n  }\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault()\n    setIsLoading(true)\n    setError('')\n\n    // Validation\n    if (formData.password !== formData.confirmPassword) {\n      setError('Les mots de passe ne correspondent pas')\n      setIsLoading(false)\n      return\n    }\n\n    if (formData.password.length < 6) {\n      setError('Le mot de passe doit contenir au moins 6 caractères')\n      setIsLoading(false)\n      return\n    }\n\n    try {\n      // TODO: Implémenter la logique d'inscription avec Supabase\n      console.log('Registration attempt:', formData)\n      \n      // Simulation d'une inscription réussie\n      setTimeout(() => {\n        router.push('/auth/login?message=Inscription réussie ! Vous pouvez maintenant vous connecter.')\n      }, 1000)\n    } catch (err) {\n      setError('Erreur lors de l\\'inscription. Veuillez réessayer.')\n    } finally {\n      setIsLoading(false)\n    }\n  }\n\n  return (\n    <div className=\"min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8\">\n      <div className=\"max-w-md w-full space-y-8\">\n        <div>\n          <div className=\"mx-auto h-12 w-12 flex items-center justify-center rounded-full bg-indigo-100\">\n            <User className=\"h-6 w-6 text-indigo-600\" />\n          </div>\n          <h2 className=\"mt-6 text-center text-3xl font-extrabold text-gray-900\">\n            Créer votre compte\n          </h2>\n          <p className=\"mt-2 text-center text-sm text-gray-600\">\n            Ou{' '}\n            <Link href=\"/auth/login\" className=\"font-medium text-indigo-600 hover:text-indigo-500\">\n              connectez-vous à votre compte existant\n            </Link>\n          </p>\n        </div>\n        \n        <form className=\"mt-8 space-y-6\" onSubmit={handleSubmit}>\n          <div className=\"space-y-4\">\n            <div className=\"grid grid-cols-2 gap-4\">\n              <div>\n                <label htmlFor=\"firstName\" className=\"block text-sm font-medium text-gray-700\">\n                  Prénom\n                </label>\n                <div className=\"mt-1 relative\">\n                  <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\n                    <User className=\"h-5 w-5 text-gray-400\" />\n                  </div>\n                  <input\n                    id=\"firstName\"\n                    name=\"firstName\"\n                    type=\"text\"\n                    required\n                    className=\"appearance-none relative block w-full px-3 py-2 pl-10 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm\"\n                    placeholder=\"Prénom\"\n                    value={formData.firstName}\n                    onChange={handleChange}\n                  />\n                </div>\n              </div>\n              \n              <div>\n                <label htmlFor=\"lastName\" className=\"block text-sm font-medium text-gray-700\">\n                  Nom\n                </label>\n                <div className=\"mt-1 relative\">\n                  <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\n                    <User className=\"h-5 w-5 text-gray-400\" />\n                  </div>\n                  <input\n                    id=\"lastName\"\n                    name=\"lastName\"\n                    type=\"text\"\n                    required\n                    className=\"appearance-none relative block w-full px-3 py-2 pl-10 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm\"\n                    placeholder=\"Nom\"\n                    value={formData.lastName}\n                    onChange={handleChange}\n                  />\n                </div>\n              </div>\n            </div>\n\n            <div>\n              <label htmlFor=\"email\" className=\"block text-sm font-medium text-gray-700\">\n                Adresse email\n              </label>\n              <div className=\"mt-1 relative\">\n                <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\n                  <Mail className=\"h-5 w-5 text-gray-400\" />\n                </div>\n                <input\n                  id=\"email\"\n                  name=\"email\"\n                  type=\"email\"\n                  autoComplete=\"email\"\n                  required\n                  className=\"appearance-none relative block w-full px-3 py-2 pl-10 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm\"\n                  placeholder=\"Adresse email\"\n                  value={formData.email}\n                  onChange={handleChange}\n                />\n              </div>\n            </div>\n\n            <div>\n              <label htmlFor=\"phone\" className=\"block text-sm font-medium text-gray-700\">\n                Téléphone (optionnel)\n              </label>\n              <div className=\"mt-1 relative\">\n                <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\n                  <Phone className=\"h-5 w-5 text-gray-400\" />\n                </div>\n                <input\n                  id=\"phone\"\n                  name=\"phone\"\n                  type=\"tel\"\n                  className=\"appearance-none relative block w-full px-3 py-2 pl-10 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm\"\n                  placeholder=\"Numéro de téléphone\"\n                  value={formData.phone}\n                  onChange={handleChange}\n                />\n              </div>\n            </div>\n\n            <div>\n              <label htmlFor=\"password\" className=\"block text-sm font-medium text-gray-700\">\n                Mot de passe\n              </label>\n              <div className=\"mt-1 relative\">\n                <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\n                  <Lock className=\"h-5 w-5 text-gray-400\" />\n                </div>\n                <input\n                  id=\"password\"\n                  name=\"password\"\n                  type={showPassword ? 'text' : 'password'}\n                  required\n                  className=\"appearance-none relative block w-full px-3 py-2 pl-10 pr-10 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm\"\n                  placeholder=\"Mot de passe\"\n                  value={formData.password}\n                  onChange={handleChange}\n                />\n                <div className=\"absolute inset-y-0 right-0 pr-3 flex items-center\">\n                  <button\n                    type=\"button\"\n                    className=\"text-gray-400 hover:text-gray-500\"\n                    onClick={() => setShowPassword(!showPassword)}\n                  >\n                    {showPassword ? (\n                      <EyeOff className=\"h-5 w-5\" />\n                    ) : (\n                      <Eye className=\"h-5 w-5\" />\n                    )}\n                  </button>\n                </div>\n              </div>\n            </div>\n\n            <div>\n              <label htmlFor=\"confirmPassword\" className=\"block text-sm font-medium text-gray-700\">\n                Confirmer le mot de passe\n              </label>\n              <div className=\"mt-1 relative\">\n                <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\n                  <Lock className=\"h-5 w-5 text-gray-400\" />\n                </div>\n                <input\n                  id=\"confirmPassword\"\n                  name=\"confirmPassword\"\n                  type={showConfirmPassword ? 'text' : 'password'}\n                  required\n                  className=\"appearance-none relative block w-full px-3 py-2 pl-10 pr-10 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm\"\n                  placeholder=\"Confirmer le mot de passe\"\n                  value={formData.confirmPassword}\n                  onChange={handleChange}\n                />\n                <div className=\"absolute inset-y-0 right-0 pr-3 flex items-center\">\n                  <button\n                    type=\"button\"\n                    className=\"text-gray-400 hover:text-gray-500\"\n                    onClick={() => setShowConfirmPassword(!showConfirmPassword)}\n                  >\n                    {showConfirmPassword ? (\n                      <EyeOff className=\"h-5 w-5\" />\n                    ) : (\n                      <Eye className=\"h-5 w-5\" />\n                    )}\n                  </button>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          {error && (\n            <div className=\"rounded-md bg-red-50 p-4\">\n              <div className=\"text-sm text-red-700\">{error}</div>\n            </div>\n          )}\n\n          <div>\n            <button\n              type=\"submit\"\n              disabled={isLoading}\n              className=\"group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50 disabled:cursor-not-allowed\"\n            >\n              {isLoading ? 'Création du compte...' : 'Créer mon compte'}\n            </button>\n          </div>\n\n          <div className=\"text-center\">\n            <p className=\"text-sm text-gray-600\">\n              Déjà un compte ?{' '}\n              <Link href=\"/auth/login\" className=\"font-medium text-indigo-600 hover:text-indigo-500\">\n                Se connecter\n              </Link>\n            </p>\n          </div>\n        </form>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AALA;;;;;;AAOe,SAAS;IACtB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,WAAW;QACX,UAAU;QACV,OAAO;QACP,OAAO;QACP,UAAU;QACV,iBAAiB;IACnB;IACA,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/D,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,eAAe,CAAC;QACpB,YAAY;YACV,GAAG,QAAQ;YACX,CAAC,EAAE,MAAM,CAAC,IAAI,CAAC,EAAE,EAAE,MAAM,CAAC,KAAK;QACjC;IACF;IAEA,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAChB,aAAa;QACb,SAAS;QAET,aAAa;QACb,IAAI,SAAS,QAAQ,KAAK,SAAS,eAAe,EAAE;YAClD,SAAS;YACT,aAAa;YACb;QACF;QAEA,IAAI,SAAS,QAAQ,CAAC,MAAM,GAAG,GAAG;YAChC,SAAS;YACT,aAAa;YACb;QACF;QAEA,IAAI;YACF,2DAA2D;YAC3D,QAAQ,GAAG,CAAC,yBAAyB;YAErC,uCAAuC;YACvC,WAAW;gBACT,OAAO,IAAI,CAAC;YACd,GAAG;QACL,EAAE,OAAO,KAAK;YACZ,SAAS;QACX,SAAU;YACR,aAAa;QACf;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;;sCACC,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,kMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;;;;;;sCAElB,8OAAC;4BAAG,WAAU;sCAAyD;;;;;;sCAGvE,8OAAC;4BAAE,WAAU;;gCAAyC;gCACjD;8CACH,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAc,WAAU;8CAAoD;;;;;;;;;;;;;;;;;;8BAM3F,8OAAC;oBAAK,WAAU;oBAAiB,UAAU;;sCACzC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;;8DACC,8OAAC;oDAAM,SAAQ;oDAAY,WAAU;8DAA0C;;;;;;8DAG/E,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC,kMAAA,CAAA,OAAI;gEAAC,WAAU;;;;;;;;;;;sEAElB,8OAAC;4DACC,IAAG;4DACH,MAAK;4DACL,MAAK;4DACL,QAAQ;4DACR,WAAU;4DACV,aAAY;4DACZ,OAAO,SAAS,SAAS;4DACzB,UAAU;;;;;;;;;;;;;;;;;;sDAKhB,8OAAC;;8DACC,8OAAC;oDAAM,SAAQ;oDAAW,WAAU;8DAA0C;;;;;;8DAG9E,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC,kMAAA,CAAA,OAAI;gEAAC,WAAU;;;;;;;;;;;sEAElB,8OAAC;4DACC,IAAG;4DACH,MAAK;4DACL,MAAK;4DACL,QAAQ;4DACR,WAAU;4DACV,aAAY;4DACZ,OAAO,SAAS,QAAQ;4DACxB,UAAU;;;;;;;;;;;;;;;;;;;;;;;;8CAMlB,8OAAC;;sDACC,8OAAC;4CAAM,SAAQ;4CAAQ,WAAU;sDAA0C;;;;;;sDAG3E,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC,kMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;;;;;;8DAElB,8OAAC;oDACC,IAAG;oDACH,MAAK;oDACL,MAAK;oDACL,cAAa;oDACb,QAAQ;oDACR,WAAU;oDACV,aAAY;oDACZ,OAAO,SAAS,KAAK;oDACrB,UAAU;;;;;;;;;;;;;;;;;;8CAKhB,8OAAC;;sDACC,8OAAC;4CAAM,SAAQ;4CAAQ,WAAU;sDAA0C;;;;;;sDAG3E,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC,oMAAA,CAAA,QAAK;wDAAC,WAAU;;;;;;;;;;;8DAEnB,8OAAC;oDACC,IAAG;oDACH,MAAK;oDACL,MAAK;oDACL,WAAU;oDACV,aAAY;oDACZ,OAAO,SAAS,KAAK;oDACrB,UAAU;;;;;;;;;;;;;;;;;;8CAKhB,8OAAC;;sDACC,8OAAC;4CAAM,SAAQ;4CAAW,WAAU;sDAA0C;;;;;;sDAG9E,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC,kMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;;;;;;8DAElB,8OAAC;oDACC,IAAG;oDACH,MAAK;oDACL,MAAM,eAAe,SAAS;oDAC9B,QAAQ;oDACR,WAAU;oDACV,aAAY;oDACZ,OAAO,SAAS,QAAQ;oDACxB,UAAU;;;;;;8DAEZ,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDACC,MAAK;wDACL,WAAU;wDACV,SAAS,IAAM,gBAAgB,CAAC;kEAE/B,6BACC,8OAAC,0MAAA,CAAA,SAAM;4DAAC,WAAU;;;;;iFAElB,8OAAC,gMAAA,CAAA,MAAG;4DAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;8CAOzB,8OAAC;;sDACC,8OAAC;4CAAM,SAAQ;4CAAkB,WAAU;sDAA0C;;;;;;sDAGrF,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC,kMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;;;;;;8DAElB,8OAAC;oDACC,IAAG;oDACH,MAAK;oDACL,MAAM,sBAAsB,SAAS;oDACrC,QAAQ;oDACR,WAAU;oDACV,aAAY;oDACZ,OAAO,SAAS,eAAe;oDAC/B,UAAU;;;;;;8DAEZ,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDACC,MAAK;wDACL,WAAU;wDACV,SAAS,IAAM,uBAAuB,CAAC;kEAEtC,oCACC,8OAAC,0MAAA,CAAA,SAAM;4DAAC,WAAU;;;;;iFAElB,8OAAC,gMAAA,CAAA,MAAG;4DAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;wBAQ1B,uBACC,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;0CAAwB;;;;;;;;;;;sCAI3C,8OAAC;sCACC,cAAA,8OAAC;gCACC,MAAK;gCACL,UAAU;gCACV,WAAU;0CAET,YAAY,0BAA0B;;;;;;;;;;;sCAI3C,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAE,WAAU;;oCAAwB;oCAClB;kDACjB,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAc,WAAU;kDAAoD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASrG", "debugId": null}}]}