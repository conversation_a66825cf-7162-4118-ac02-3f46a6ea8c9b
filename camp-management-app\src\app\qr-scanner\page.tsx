'use client'

import { useState, useRef, useEffect } from 'react'
import Link from 'next/link'
import { Camera, X, CheckCircle, AlertCircle, ArrowLeft } from 'lucide-react'

export default function QRScannerPage() {
  const [isScanning, setIsScanning] = useState(false)
  const [scanResult, setScanResult] = useState<string | null>(null)
  const [error, setError] = useState<string | null>(null)
  const [hasPermission, setHasPermission] = useState<boolean | null>(null)
  const videoRef = useRef<HTMLVideoElement>(null)
  const streamRef = useRef<MediaStream | null>(null)

  useEffect(() => {
    return () => {
      // Nettoyer le stream quand le composant est démonté
      if (streamRef.current) {
        streamRef.current.getTracks().forEach(track => track.stop())
      }
    }
  }, [])

  const startScanning = async () => {
    try {
      setError(null)
      
      // Demander l'accès à la caméra
      const stream = await navigator.mediaDevices.getUserMedia({
        video: { facingMode: 'environment' } // Utiliser la caméra arrière si disponible
      })
      
      streamRef.current = stream
      setHasPermission(true)
      
      if (videoRef.current) {
        videoRef.current.srcObject = stream
        videoRef.current.play()
      }
      
      setIsScanning(true)
      
      // TODO: Intégrer une vraie bibliothèque de scan QR comme qr-scanner
      // Pour l'instant, simulation d'un scan après 3 secondes
      setTimeout(() => {
        setScanResult('event_12345_user_67890')
        setIsScanning(false)
        stopScanning()
      }, 3000)
      
    } catch (err) {
      console.error('Erreur d\'accès à la caméra:', err)
      setError('Impossible d\'accéder à la caméra. Vérifiez les permissions.')
      setHasPermission(false)
    }
  }

  const stopScanning = () => {
    if (streamRef.current) {
      streamRef.current.getTracks().forEach(track => track.stop())
      streamRef.current = null
    }
    
    if (videoRef.current) {
      videoRef.current.srcObject = null
    }
    
    setIsScanning(false)
  }

  const resetScanner = () => {
    setScanResult(null)
    setError(null)
    stopScanning()
  }

  const processQRCode = async (qrData: string) => {
    try {
      // TODO: Envoyer le QR code au serveur pour traitement
      console.log('QR Code scanné:', qrData)
      
      // Simulation du traitement
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      // Ici, on traiterait le QR code selon son type
      // Par exemple: event_12345_user_67890 pour un événement
      
    } catch (err) {
      setError('Erreur lors du traitement du QR code')
    }
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between py-6">
            <div className="flex items-center">
              <Link
                href="/dashboard"
                className="mr-4 p-2 text-gray-400 hover:text-gray-500"
              >
                <ArrowLeft className="h-6 w-6" />
              </Link>
              <h1 className="text-2xl font-bold text-gray-900">Scanner QR Code</h1>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-2xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
        <div className="bg-white rounded-lg shadow-lg overflow-hidden">
          {/* Scanner Area */}
          <div className="relative bg-black aspect-square">
            {isScanning ? (
              <>
                <video
                  ref={videoRef}
                  className="w-full h-full object-cover"
                  autoPlay
                  playsInline
                  muted
                />
                
                {/* Overlay de scan */}
                <div className="absolute inset-0 flex items-center justify-center">
                  <div className="w-64 h-64 border-2 border-white border-dashed rounded-lg flex items-center justify-center">
                    <div className="text-white text-center">
                      <Camera className="h-8 w-8 mx-auto mb-2" />
                      <p className="text-sm">Positionnez le QR code dans le cadre</p>
                    </div>
                  </div>
                </div>
                
                {/* Bouton d'arrêt */}
                <button
                  onClick={stopScanning}
                  className="absolute top-4 right-4 p-2 bg-red-600 text-white rounded-full hover:bg-red-700"
                >
                  <X className="h-6 w-6" />
                </button>
              </>
            ) : (
              <div className="w-full h-full flex items-center justify-center">
                <div className="text-center text-white">
                  <Camera className="h-16 w-16 mx-auto mb-4 text-gray-400" />
                  <p className="text-gray-400 mb-4">Caméra non active</p>
                  
                  {!isScanning && !scanResult && !error && (
                    <button
                      onClick={startScanning}
                      className="bg-indigo-600 hover:bg-indigo-700 text-white px-6 py-3 rounded-md font-medium"
                    >
                      Démarrer le scan
                    </button>
                  )}
                </div>
              </div>
            )}
          </div>

          {/* Results Area */}
          <div className="p-6">
            {error && (
              <div className="mb-4 p-4 bg-red-50 border border-red-200 rounded-md">
                <div className="flex">
                  <AlertCircle className="h-5 w-5 text-red-400 mr-2 mt-0.5" />
                  <div>
                    <h3 className="text-sm font-medium text-red-800">Erreur</h3>
                    <p className="text-sm text-red-700 mt-1">{error}</p>
                  </div>
                </div>
              </div>
            )}

            {scanResult && (
              <div className="mb-4 p-4 bg-green-50 border border-green-200 rounded-md">
                <div className="flex">
                  <CheckCircle className="h-5 w-5 text-green-400 mr-2 mt-0.5" />
                  <div className="flex-1">
                    <h3 className="text-sm font-medium text-green-800">QR Code scanné avec succès</h3>
                    <p className="text-sm text-green-700 mt-1 font-mono break-all">{scanResult}</p>
                    
                    <div className="mt-3 flex space-x-3">
                      <button
                        onClick={() => processQRCode(scanResult)}
                        className="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-md text-sm font-medium"
                      >
                        Traiter
                      </button>
                      <button
                        onClick={resetScanner}
                        className="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-md text-sm font-medium"
                      >
                        Scanner à nouveau
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {hasPermission === false && (
              <div className="mb-4 p-4 bg-yellow-50 border border-yellow-200 rounded-md">
                <div className="flex">
                  <AlertCircle className="h-5 w-5 text-yellow-400 mr-2 mt-0.5" />
                  <div>
                    <h3 className="text-sm font-medium text-yellow-800">Permission requise</h3>
                    <p className="text-sm text-yellow-700 mt-1">
                      L'accès à la caméra est nécessaire pour scanner les QR codes. 
                      Veuillez autoriser l'accès dans les paramètres de votre navigateur.
                    </p>
                  </div>
                </div>
              </div>
            )}

            {/* Instructions */}
            <div className="bg-gray-50 rounded-md p-4">
              <h3 className="text-sm font-medium text-gray-900 mb-2">Instructions</h3>
              <ul className="text-sm text-gray-600 space-y-1">
                <li>• Autorisez l'accès à la caméra lorsque demandé</li>
                <li>• Positionnez le QR code dans le cadre de scan</li>
                <li>• Maintenez l'appareil stable pour une meilleure lecture</li>
                <li>• Assurez-vous d'avoir un bon éclairage</li>
              </ul>
            </div>
          </div>
        </div>
      </main>
    </div>
  )
}
