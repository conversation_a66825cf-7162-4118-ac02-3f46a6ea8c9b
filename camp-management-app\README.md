# CampManager - Application de Gestion de Camp

Une application web moderne et complète pour la gestion de camps, excursions et activités de groupe. Développée avec Next.js 14, TypeScript, Prisma et Supabase.

## 🚀 Fonctionnalités

### ✅ Fonctionnalités Implémentées

- **Page d'accueil moderne** avec présentation des fonctionnalités
- **Système d'authentification** (inscription/connexion)
- **Tableau de bord** avec statistiques et actions rapides
- **Gestion des événements** (création, visualisation)
- **Scanner QR Code** pour le contrôle d'accès
- **Interface responsive** adaptée mobile et desktop
- **Base de données** avec schéma complet (Prisma)
- **API REST** pour la gestion des données

### 🔄 Fonctionnalités en Développement

- Chat en temps réel avec Socket.io
- Galerie photo sécurisée
- Notifications push
- Gestion des groupes et sous-groupes
- Système de présences avancé
- Rapports et statistiques détaillés

## 🛠️ Technologies Utilisées

- **Frontend**: Next.js 14, React, TypeScript, Tailwind CSS
- **Backend**: Next.js API Routes, Prisma ORM
- **Base de données**: PostgreSQL
- **Authentification**: Supabase Auth
- **Stockage**: Supabase Storage
- **UI/UX**: Lucide React Icons, Headless UI
- **Validation**: Zod
- **Styling**: Tailwind CSS

## 📋 Prérequis

- Node.js 18+
- PostgreSQL
- Compte Supabase (gratuit)

## 🚀 Installation

1. **Cloner le projet**
```bash
git clone <repository-url>
cd camp-management-app
```

2. **Installer les dépendances**
```bash
npm install
```

3. **Configuration de l'environnement**
Créer un fichier `.env.local` à la racine du projet :

```env
# Base de données
DATABASE_URL="postgresql://username:password@localhost:5432/camp_management?schema=public"

# Supabase
NEXT_PUBLIC_SUPABASE_URL="your_supabase_url"
NEXT_PUBLIC_SUPABASE_ANON_KEY="your_supabase_anon_key"
SUPABASE_SERVICE_ROLE_KEY="your_supabase_service_role_key"

# NextAuth.js
NEXTAUTH_URL="http://localhost:3000"
NEXTAUTH_SECRET="your_nextauth_secret"
```

4. **Configuration de la base de données**
```bash
# Générer le client Prisma
npx prisma generate

# Appliquer les migrations
npx prisma migrate dev --name init

# (Optionnel) Visualiser la base de données
npx prisma studio
```

5. **Démarrer le serveur de développement**
```bash
npm run dev
```

L'application sera accessible sur [http://localhost:3000](http://localhost:3000)

## 📁 Structure du Projet

```
camp-management-app/
├── src/
│   ├── app/                    # Pages et API routes (App Router)
│   │   ├── api/               # API endpoints
│   │   ├── auth/              # Pages d'authentification
│   │   ├── dashboard/         # Tableau de bord
│   │   ├── events/            # Gestion des événements
│   │   └── qr-scanner/        # Scanner QR Code
│   ├── components/            # Composants réutilisables
│   │   └── ui/               # Composants UI de base
│   ├── lib/                   # Utilitaires et configurations
│   └── types/                 # Types TypeScript
├── prisma/                    # Schéma et migrations de base de données
└── public/                    # Assets statiques
```

## 🗄️ Schéma de Base de Données

Le schéma inclut les modèles principaux :

- **Users** : Gestion des utilisateurs avec rôles
- **Events** : Événements/camps avec QR codes
- **Participations** : Inscriptions aux événements
- **Groups** : Groupes et sous-groupes
- **Activities** : Activités programmées
- **Messages** : Système de chat
- **AttendanceRecords** : Suivi des présences
- **MediaAlbums** : Galeries photo

## 🔧 Scripts Disponibles

```bash
# Développement
npm run dev

# Build de production
npm run build

# Démarrer en production
npm start

# Linting
npm run lint

# Base de données
npx prisma studio          # Interface graphique DB
npx prisma migrate dev      # Nouvelle migration
npx prisma generate         # Générer le client
```

## 🌐 API Endpoints

### Authentification
- `POST /api/auth/register` - Inscription
- `POST /api/auth/login` - Connexion

### Événements
- `GET /api/events` - Liste des événements
- `POST /api/events` - Créer un événement
- `GET /api/events/[id]` - Détails d'un événement
- `PUT /api/events/[id]` - Modifier un événement

### Utilisateurs
- `GET /api/users` - Liste des utilisateurs
- `GET /api/users/[id]` - Profil utilisateur
- `PUT /api/users/[id]` - Modifier le profil

## 🎨 Interface Utilisateur

L'application propose une interface moderne avec :

- **Design responsive** adapté à tous les écrans
- **Thème cohérent** avec palette de couleurs professionnelle
- **Navigation intuitive** avec breadcrumbs et menus contextuels
- **Feedback utilisateur** avec notifications et états de chargement
- **Accessibilité** respectant les standards WCAG

## 🔐 Sécurité

- Authentification sécurisée avec Supabase
- Validation des données avec Zod
- Protection CSRF intégrée
- Gestion des permissions par rôles
- Chiffrement des données sensibles

## 📱 Fonctionnalités Mobiles

- Interface responsive optimisée mobile
- Scanner QR Code avec accès caméra
- Notifications push (à venir)
- Mode hors-ligne partiel (à venir)

## 🤝 Contribution

1. Fork le projet
2. Créer une branche feature (`git checkout -b feature/AmazingFeature`)
3. Commit les changements (`git commit -m 'Add some AmazingFeature'`)
4. Push vers la branche (`git push origin feature/AmazingFeature`)
5. Ouvrir une Pull Request

## 📄 Licence

Ce projet est sous licence MIT. Voir le fichier `LICENSE` pour plus de détails.

## 📞 Support

Pour toute question ou problème :
- Créer une issue sur GitHub
- Consulter la documentation
- Contacter l'équipe de développement

---

**CampManager** - Simplifiez la gestion de vos camps et événements ! 🏕️
