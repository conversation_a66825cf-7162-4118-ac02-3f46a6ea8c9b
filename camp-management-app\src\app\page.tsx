'use client'

import { useState } from 'react'
import Link from 'next/link'
import {
  Users,
  Calendar,
  MessageCircle,
  Camera,
  QrCode,
  BarChart3,
  Shield,
  Smartphone,
  Globe,
  CheckCircle
} from 'lucide-react'

export default function Home() {
  const [activeFeature, setActiveFeature] = useState(0)

  const features = [
    {
      icon: <Users className="w-8 h-8" />,
      title: "Gestion des utilisateurs",
      description: "Inscription simple, profils détaillés, gestion des groupes et familles"
    },
    {
      icon: <QrCode className="w-8 h-8" />,
      title: "Contrôle d'accès QR",
      description: "Scan QR code pour entrées/sorties, suivi en temps réel des présences"
    },
    {
      icon: <MessageCircle className="w-8 h-8" />,
      title: "Communication temps réel",
      description: "Chat privé, groupes de discussion, notifications push personnalisées"
    },
    {
      icon: <Calendar className="w-8 h-8" />,
      title: "Planification activités",
      description: "Création de planning, partage de documents, informations actualisées"
    },
    {
      icon: <Camera className="w-8 h-8" />,
      title: "Galerie photo sécurisée",
      description: "Partage de photos/vidéos avec contrôle d'accès et interactions"
    },
    {
      icon: <BarChart3 className="w-8 h-8" />,
      title: "Tableau de bord",
      description: "Statistiques, indicateurs clés, rapports de participation"
    }
  ]

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
      {/* Header */}
      <header className="bg-white shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <h1 className="text-2xl font-bold text-indigo-600">CampManager</h1>
              </div>
            </div>
            <div className="flex items-center space-x-4">
              <Link
                href="/auth/login"
                className="text-gray-500 hover:text-gray-700 px-3 py-2 rounded-md text-sm font-medium"
              >
                Connexion
              </Link>
              <Link
                href="/auth/register"
                className="bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded-md text-sm font-medium"
              >
                S'inscrire
              </Link>
            </div>
          </div>
        </div>
      </header>

      {/* Hero Section */}
      <section className="py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <h1 className="text-4xl tracking-tight font-extrabold text-gray-900 sm:text-5xl md:text-6xl">
              <span className="block">Gestion de camp</span>
              <span className="block text-indigo-600">simplifiée</span>
            </h1>
            <p className="mt-3 max-w-md mx-auto text-base text-gray-500 sm:text-lg md:mt-5 md:text-xl md:max-w-3xl">
              Une application complète pour organiser vos camps, excursions et activités de groupe.
              Communication en temps réel, contrôle d'accès sécurisé et gestion centralisée.
            </p>
            <div className="mt-5 max-w-md mx-auto sm:flex sm:justify-center md:mt-8">
              <div className="rounded-md shadow">
                <Link
                  href="/auth/register"
                  className="w-full flex items-center justify-center px-8 py-3 border border-transparent text-base font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 md:py-4 md:text-lg md:px-10"
                >
                  Commencer gratuitement
                </Link>
              </div>
              <div className="mt-3 rounded-md shadow sm:mt-0 sm:ml-3">
                <Link
                  href="#features"
                  className="w-full flex items-center justify-center px-8 py-3 border border-transparent text-base font-medium rounded-md text-indigo-600 bg-white hover:bg-gray-50 md:py-4 md:text-lg md:px-10"
                >
                  Découvrir
                </Link>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section id="features" className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <h2 className="text-3xl font-extrabold text-gray-900">
              Fonctionnalités principales
            </h2>
            <p className="mt-4 text-lg text-gray-500">
              Tout ce dont vous avez besoin pour gérer vos événements
            </p>
          </div>

          <div className="mt-16">
            <div className="grid grid-cols-1 gap-8 sm:grid-cols-2 lg:grid-cols-3">
              {features.map((feature, index) => (
                <div
                  key={index}
                  className="relative group bg-white p-6 focus-within:ring-2 focus-within:ring-inset focus-within:ring-indigo-500 rounded-lg shadow-md hover:shadow-lg transition-shadow"
                >
                  <div>
                    <span className="rounded-lg inline-flex p-3 bg-indigo-50 text-indigo-600 group-hover:bg-indigo-100">
                      {feature.icon}
                    </span>
                  </div>
                  <div className="mt-8">
                    <h3 className="text-lg font-medium text-gray-900">
                      {feature.title}
                    </h3>
                    <p className="mt-2 text-sm text-gray-500">
                      {feature.description}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Benefits Section */}
      <section className="py-16 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="lg:grid lg:grid-cols-2 lg:gap-8 lg:items-center">
            <div>
              <h2 className="text-3xl font-extrabold text-gray-900">
                Pourquoi choisir CampManager ?
              </h2>
              <p className="mt-3 text-lg text-gray-500">
                Une solution complète pensée pour les organisateurs et les participants
              </p>

              <dl className="mt-10 space-y-10">
                <div className="flex">
                  <div className="flex-shrink-0">
                    <div className="flex items-center justify-center h-12 w-12 rounded-md bg-indigo-500 text-white">
                      <Shield className="h-6 w-6" />
                    </div>
                  </div>
                  <div className="ml-4">
                    <dt className="text-lg leading-6 font-medium text-gray-900">
                      Sécurité renforcée
                    </dt>
                    <dd className="mt-2 text-base text-gray-500">
                      Contrôle d'accès par QR code, gestion des droits utilisateurs, données chiffrées
                    </dd>
                  </div>
                </div>

                <div className="flex">
                  <div className="flex-shrink-0">
                    <div className="flex items-center justify-center h-12 w-12 rounded-md bg-indigo-500 text-white">
                      <Smartphone className="h-6 w-6" />
                    </div>
                  </div>
                  <div className="ml-4">
                    <dt className="text-lg leading-6 font-medium text-gray-900">
                      Multi-plateforme
                    </dt>
                    <dd className="mt-2 text-base text-gray-500">
                      Accessible sur mobile, tablette et ordinateur. Synchronisation en temps réel
                    </dd>
                  </div>
                </div>

                <div className="flex">
                  <div className="flex-shrink-0">
                    <div className="flex items-center justify-center h-12 w-12 rounded-md bg-indigo-500 text-white">
                      <Globe className="h-6 w-6" />
                    </div>
                  </div>
                  <div className="ml-4">
                    <dt className="text-lg leading-6 font-medium text-gray-900">
                      Accessible partout
                    </dt>
                    <dd className="mt-2 text-base text-gray-500">
                      Fonctionne même avec une connexion limitée. Données synchronisées automatiquement
                    </dd>
                  </div>
                </div>
              </dl>
            </div>

            <div className="mt-10 lg:mt-0">
              <div className="bg-white rounded-lg shadow-lg p-8">
                <h3 className="text-xl font-semibold text-gray-900 mb-6">
                  Fonctionnalités incluses
                </h3>
                <ul className="space-y-4">
                  {[
                    "Gestion complète des utilisateurs",
                    "Système de QR codes",
                    "Chat en temps réel",
                    "Galerie photo sécurisée",
                    "Notifications push",
                    "Tableau de bord analytique",
                    "Gestion des documents",
                    "Support multi-langues"
                  ].map((item, index) => (
                    <li key={index} className="flex items-center">
                      <CheckCircle className="h-5 w-5 text-green-500 mr-3" />
                      <span className="text-gray-700">{item}</span>
                    </li>
                  ))}
                </ul>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-gray-800">
        <div className="max-w-7xl mx-auto py-12 px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <h3 className="text-2xl font-bold text-white">CampManager</h3>
            <p className="mt-2 text-gray-300">
              La solution complète pour la gestion de vos camps et événements
            </p>
            <div className="mt-8">
              <Link
                href="/auth/register"
                className="bg-indigo-600 hover:bg-indigo-700 text-white px-8 py-3 rounded-md text-lg font-medium"
              >
                Commencer maintenant
              </Link>
            </div>
          </div>
        </div>
      </footer>
    </div>
  )
}
