// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// Énumérations
enum UserRole {
  ADMIN
  ORGANIZER
  PARTICIPANT
  FAMILY
}

enum EventStatus {
  DRAFT
  PUBLISHED
  ACTIVE
  COMPLETED
  CANCELLED
}

enum AttendanceStatus {
  PRESENT
  ABSENT
  LATE
}

enum MessageType {
  TEXT
  IMAGE
  FILE
  SYSTEM
}

// Modèles principaux
model User {
  id          String   @id @default(cuid())
  email       String   @unique
  firstName   String
  lastName    String
  phone       String?
  role        UserRole @default(PARTICIPANT)
  avatar      String?
  isActive    Boolean  @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  profile           UserProfile?
  organizedEvents   Event[]           @relation("EventOrganizer")
  participations    Participation[]
  sentMessages      Message[]         @relation("MessageSender")
  attendanceRecords AttendanceRecord[]
  emergencyContacts EmergencyContact[]
  familyMembers     FamilyMember[]    @relation("FamilyHead")
  familyOf          FamilyMember[]    @relation("FamilyMemberUser")

  @@map("users")
}

model UserProfile {
  id           String    @id @default(cuid())
  userId       String    @unique
  dateOfBirth  DateTime?
  address      String?
  allergies    String?
  medicalInfo  String?
  emergencyContact String?
  preferences  Json?

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("user_profiles")
}

model Event {
  id          String      @id @default(cuid())
  title       String
  description String?
  startDate   DateTime
  endDate     DateTime
  location    String?
  maxParticipants Int?
  status      EventStatus @default(DRAFT)
  organizerId String
  qrCode      String?     @unique
  createdAt   DateTime    @default(now())
  updatedAt   DateTime    @updatedAt

  // Relations
  organizer      User            @relation("EventOrganizer", fields: [organizerId], references: [id])
  participations Participation[]
  activities     Activity[]
  groups         Group[]
  chatRooms      ChatRoom[]
  mediaAlbums    MediaAlbum[]
  attendanceRecords AttendanceRecord[]

  @@map("events")
}

model Participation {
  id        String   @id @default(cuid())
  userId    String
  eventId   String
  status    String   @default("PENDING") // PENDING, CONFIRMED, CANCELLED
  registeredAt DateTime @default(now())

  user  User  @relation(fields: [userId], references: [id], onDelete: Cascade)
  event Event @relation(fields: [eventId], references: [id], onDelete: Cascade)

  @@unique([userId, eventId])
  @@map("participations")
}

model Group {
  id          String @id @default(cuid())
  name        String
  description String?
  eventId     String
  color       String? // Pour l'interface utilisateur
  createdAt   DateTime @default(now())

  event     Event       @relation(fields: [eventId], references: [id], onDelete: Cascade)
  members   GroupMember[]
  activities Activity[]
  chatRooms ChatRoom[]

  @@map("groups")
}

model GroupMember {
  id      String @id @default(cuid())
  groupId String
  userId  String
  role    String @default("MEMBER") // LEADER, MEMBER
  joinedAt DateTime @default(now())

  group Group @relation(fields: [groupId], references: [id], onDelete: Cascade)

  @@unique([groupId, userId])
  @@map("group_members")
}

model Activity {
  id          String   @id @default(cuid())
  title       String
  description String?
  startTime   DateTime
  endTime     DateTime
  location    String?
  eventId     String
  groupId     String?
  documents   Json? // URLs des documents associés
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  event Event @relation(fields: [eventId], references: [id], onDelete: Cascade)
  group Group? @relation(fields: [groupId], references: [id])
  attendanceRecords AttendanceRecord[]

  @@map("activities")
}

model AttendanceRecord {
  id         String           @id @default(cuid())
  userId     String
  eventId    String?
  activityId String?
  status     AttendanceStatus
  checkInTime DateTime?
  checkOutTime DateTime?
  location   String?
  qrCodeUsed String?
  createdAt  DateTime         @default(now())

  user     User      @relation(fields: [userId], references: [id], onDelete: Cascade)
  event    Event?    @relation(fields: [eventId], references: [id])
  activity Activity? @relation(fields: [activityId], references: [id])

  @@map("attendance_records")
}

model ChatRoom {
  id          String @id @default(cuid())
  name        String
  description String?
  type        String // PUBLIC, PRIVATE, GROUP
  eventId     String?
  groupId     String?
  isActive    Boolean @default(true)
  createdAt   DateTime @default(now())

  event    Event?    @relation(fields: [eventId], references: [id])
  group    Group?    @relation(fields: [groupId], references: [id])
  messages Message[]
  members  ChatRoomMember[]

  @@map("chat_rooms")
}

model ChatRoomMember {
  id         String   @id @default(cuid())
  chatRoomId String
  userId     String
  role       String   @default("MEMBER") // ADMIN, MODERATOR, MEMBER
  joinedAt   DateTime @default(now())
  lastReadAt DateTime?

  chatRoom ChatRoom @relation(fields: [chatRoomId], references: [id], onDelete: Cascade)

  @@unique([chatRoomId, userId])
  @@map("chat_room_members")
}

model Message {
  id         String      @id @default(cuid())
  content    String
  type       MessageType @default(TEXT)
  senderId   String
  chatRoomId String
  replyToId  String?
  attachments Json? // URLs des fichiers attachés
  isEdited   Boolean     @default(false)
  createdAt  DateTime    @default(now())
  updatedAt  DateTime    @updatedAt

  sender   User     @relation("MessageSender", fields: [senderId], references: [id])
  chatRoom ChatRoom @relation(fields: [chatRoomId], references: [id], onDelete: Cascade)
  replyTo  Message? @relation("MessageReply", fields: [replyToId], references: [id])
  replies  Message[] @relation("MessageReply")

  @@map("messages")
}

model MediaAlbum {
  id          String @id @default(cuid())
  title       String
  description String?
  eventId     String
  isPublic    Boolean @default(false)
  createdAt   DateTime @default(now())

  event Event @relation(fields: [eventId], references: [id], onDelete: Cascade)
  media MediaFile[]

  @@map("media_albums")
}

model MediaFile {
  id          String @id @default(cuid())
  filename    String
  originalName String
  mimeType    String
  size        Int
  url         String
  albumId     String
  uploadedBy  String
  createdAt   DateTime @default(now())

  album MediaAlbum @relation(fields: [albumId], references: [id], onDelete: Cascade)

  @@map("media_files")
}

model EmergencyContact {
  id           String @id @default(cuid())
  userId       String
  name         String
  relationship String
  phone        String
  email        String?
  isPrimary    Boolean @default(false)

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("emergency_contacts")
}

model FamilyMember {
  id           String @id @default(cuid())
  familyHeadId String
  memberId     String
  relationship String // PARENT, CHILD, SIBLING, etc.
  createdAt    DateTime @default(now())

  familyHead User @relation("FamilyHead", fields: [familyHeadId], references: [id])
  member     User @relation("FamilyMemberUser", fields: [memberId], references: [id])

  @@unique([familyHeadId, memberId])
  @@map("family_members")
}
