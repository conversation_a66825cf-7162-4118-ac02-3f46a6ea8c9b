{"version": 3, "sources": [], "sections": [{"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/xampp1/htdocs/WIIZE%20SAB/camp-management-app/src/app/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport Link from 'next/link'\nimport {\n  Users,\n  Calendar,\n  MessageCircle,\n  Camera,\n  QrCode,\n  BarChart3,\n  Shield,\n  Smartphone,\n  Globe,\n  CheckCircle\n} from 'lucide-react'\n\nexport default function Home() {\n  const [activeFeature, setActiveFeature] = useState(0)\n\n  const features = [\n    {\n      icon: <Users className=\"w-8 h-8\" />,\n      title: \"Gestion des utilisateurs\",\n      description: \"Inscription simple, profils détaillés, gestion des groupes et familles\"\n    },\n    {\n      icon: <QrCode className=\"w-8 h-8\" />,\n      title: \"Contrôle d'accès QR\",\n      description: \"Scan QR code pour entrées/sorties, suivi en temps réel des présences\"\n    },\n    {\n      icon: <MessageCircle className=\"w-8 h-8\" />,\n      title: \"Communication temps réel\",\n      description: \"Chat privé, groupes de discussion, notifications push personnalisées\"\n    },\n    {\n      icon: <Calendar className=\"w-8 h-8\" />,\n      title: \"Planification activités\",\n      description: \"Création de planning, partage de documents, informations actualisées\"\n    },\n    {\n      icon: <Camera className=\"w-8 h-8\" />,\n      title: \"Galerie photo sécurisée\",\n      description: \"Partage de photos/vidéos avec contrôle d'accès et interactions\"\n    },\n    {\n      icon: <BarChart3 className=\"w-8 h-8\" />,\n      title: \"Tableau de bord\",\n      description: \"Statistiques, indicateurs clés, rapports de participation\"\n    }\n  ]\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100\">\n      {/* Header */}\n      <header className=\"bg-white shadow-sm\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex justify-between items-center py-6\">\n            <div className=\"flex items-center\">\n              <div className=\"flex-shrink-0\">\n                <h1 className=\"text-2xl font-bold text-indigo-600\">CampManager</h1>\n              </div>\n            </div>\n            <div className=\"flex items-center space-x-4\">\n              <Link\n                href=\"/auth/login\"\n                className=\"text-gray-500 hover:text-gray-700 px-3 py-2 rounded-md text-sm font-medium\"\n              >\n                Connexion\n              </Link>\n              <Link\n                href=\"/auth/register\"\n                className=\"bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded-md text-sm font-medium\"\n              >\n                S'inscrire\n              </Link>\n            </div>\n          </div>\n        </div>\n      </header>\n\n      {/* Hero Section */}\n      <section className=\"py-20\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"text-center\">\n            <h1 className=\"text-4xl tracking-tight font-extrabold text-gray-900 sm:text-5xl md:text-6xl\">\n              <span className=\"block\">Gestion de camp</span>\n              <span className=\"block text-indigo-600\">simplifiée</span>\n            </h1>\n            <p className=\"mt-3 max-w-md mx-auto text-base text-gray-500 sm:text-lg md:mt-5 md:text-xl md:max-w-3xl\">\n              Une application complète pour organiser vos camps, excursions et activités de groupe.\n              Communication en temps réel, contrôle d'accès sécurisé et gestion centralisée.\n            </p>\n            <div className=\"mt-5 max-w-md mx-auto sm:flex sm:justify-center md:mt-8\">\n              <div className=\"rounded-md shadow\">\n                <Link\n                  href=\"/auth/register\"\n                  className=\"w-full flex items-center justify-center px-8 py-3 border border-transparent text-base font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 md:py-4 md:text-lg md:px-10\"\n                >\n                  Commencer gratuitement\n                </Link>\n              </div>\n              <div className=\"mt-3 rounded-md shadow sm:mt-0 sm:ml-3\">\n                <Link\n                  href=\"#features\"\n                  className=\"w-full flex items-center justify-center px-8 py-3 border border-transparent text-base font-medium rounded-md text-indigo-600 bg-white hover:bg-gray-50 md:py-4 md:text-lg md:px-10\"\n                >\n                  Découvrir\n                </Link>\n              </div>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* Features Section */}\n      <section id=\"features\" className=\"py-16 bg-white\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"text-center\">\n            <h2 className=\"text-3xl font-extrabold text-gray-900\">\n              Fonctionnalités principales\n            </h2>\n            <p className=\"mt-4 text-lg text-gray-500\">\n              Tout ce dont vous avez besoin pour gérer vos événements\n            </p>\n          </div>\n\n          <div className=\"mt-16\">\n            <div className=\"grid grid-cols-1 gap-8 sm:grid-cols-2 lg:grid-cols-3\">\n              {features.map((feature, index) => (\n                <div\n                  key={index}\n                  className=\"relative group bg-white p-6 focus-within:ring-2 focus-within:ring-inset focus-within:ring-indigo-500 rounded-lg shadow-md hover:shadow-lg transition-shadow\"\n                >\n                  <div>\n                    <span className=\"rounded-lg inline-flex p-3 bg-indigo-50 text-indigo-600 group-hover:bg-indigo-100\">\n                      {feature.icon}\n                    </span>\n                  </div>\n                  <div className=\"mt-8\">\n                    <h3 className=\"text-lg font-medium text-gray-900\">\n                      {feature.title}\n                    </h3>\n                    <p className=\"mt-2 text-sm text-gray-500\">\n                      {feature.description}\n                    </p>\n                  </div>\n                </div>\n              ))}\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* Benefits Section */}\n      <section className=\"py-16 bg-gray-50\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"lg:grid lg:grid-cols-2 lg:gap-8 lg:items-center\">\n            <div>\n              <h2 className=\"text-3xl font-extrabold text-gray-900\">\n                Pourquoi choisir CampManager ?\n              </h2>\n              <p className=\"mt-3 text-lg text-gray-500\">\n                Une solution complète pensée pour les organisateurs et les participants\n              </p>\n\n              <dl className=\"mt-10 space-y-10\">\n                <div className=\"flex\">\n                  <div className=\"flex-shrink-0\">\n                    <div className=\"flex items-center justify-center h-12 w-12 rounded-md bg-indigo-500 text-white\">\n                      <Shield className=\"h-6 w-6\" />\n                    </div>\n                  </div>\n                  <div className=\"ml-4\">\n                    <dt className=\"text-lg leading-6 font-medium text-gray-900\">\n                      Sécurité renforcée\n                    </dt>\n                    <dd className=\"mt-2 text-base text-gray-500\">\n                      Contrôle d'accès par QR code, gestion des droits utilisateurs, données chiffrées\n                    </dd>\n                  </div>\n                </div>\n\n                <div className=\"flex\">\n                  <div className=\"flex-shrink-0\">\n                    <div className=\"flex items-center justify-center h-12 w-12 rounded-md bg-indigo-500 text-white\">\n                      <Smartphone className=\"h-6 w-6\" />\n                    </div>\n                  </div>\n                  <div className=\"ml-4\">\n                    <dt className=\"text-lg leading-6 font-medium text-gray-900\">\n                      Multi-plateforme\n                    </dt>\n                    <dd className=\"mt-2 text-base text-gray-500\">\n                      Accessible sur mobile, tablette et ordinateur. Synchronisation en temps réel\n                    </dd>\n                  </div>\n                </div>\n\n                <div className=\"flex\">\n                  <div className=\"flex-shrink-0\">\n                    <div className=\"flex items-center justify-center h-12 w-12 rounded-md bg-indigo-500 text-white\">\n                      <Globe className=\"h-6 w-6\" />\n                    </div>\n                  </div>\n                  <div className=\"ml-4\">\n                    <dt className=\"text-lg leading-6 font-medium text-gray-900\">\n                      Accessible partout\n                    </dt>\n                    <dd className=\"mt-2 text-base text-gray-500\">\n                      Fonctionne même avec une connexion limitée. Données synchronisées automatiquement\n                    </dd>\n                  </div>\n                </div>\n              </dl>\n            </div>\n\n            <div className=\"mt-10 lg:mt-0\">\n              <div className=\"bg-white rounded-lg shadow-lg p-8\">\n                <h3 className=\"text-xl font-semibold text-gray-900 mb-6\">\n                  Fonctionnalités incluses\n                </h3>\n                <ul className=\"space-y-4\">\n                  {[\n                    \"Gestion complète des utilisateurs\",\n                    \"Système de QR codes\",\n                    \"Chat en temps réel\",\n                    \"Galerie photo sécurisée\",\n                    \"Notifications push\",\n                    \"Tableau de bord analytique\",\n                    \"Gestion des documents\",\n                    \"Support multi-langues\"\n                  ].map((item, index) => (\n                    <li key={index} className=\"flex items-center\">\n                      <CheckCircle className=\"h-5 w-5 text-green-500 mr-3\" />\n                      <span className=\"text-gray-700\">{item}</span>\n                    </li>\n                  ))}\n                </ul>\n              </div>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* Footer */}\n      <footer className=\"bg-gray-800\">\n        <div className=\"max-w-7xl mx-auto py-12 px-4 sm:px-6 lg:px-8\">\n          <div className=\"text-center\">\n            <h3 className=\"text-2xl font-bold text-white\">CampManager</h3>\n            <p className=\"mt-2 text-gray-300\">\n              La solution complète pour la gestion de vos camps et événements\n            </p>\n            <div className=\"mt-8\">\n              <Link\n                href=\"/auth/register\"\n                className=\"bg-indigo-600 hover:bg-indigo-700 text-white px-8 py-3 rounded-md text-lg font-medium\"\n              >\n                Commencer maintenant\n              </Link>\n            </div>\n          </div>\n        </div>\n      </footer>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAJA;;;;;AAiBe,SAAS;IACtB,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEnD,MAAM,WAAW;QACf;YACE,oBAAM,8OAAC,oMAAA,CAAA,QAAK;gBAAC,WAAU;;;;;;YACvB,OAAO;YACP,aAAa;QACf;QACA;YACE,oBAAM,8OAAC,0MAAA,CAAA,SAAM;gBAAC,WAAU;;;;;;YACxB,OAAO;YACP,aAAa;QACf;QACA;YACE,oBAAM,8OAAC,wNAAA,CAAA,gBAAa;gBAAC,WAAU;;;;;;YAC/B,OAAO;YACP,aAAa;QACf;QACA;YACE,oBAAM,8OAAC,0MAAA,CAAA,WAAQ;gBAAC,WAAU;;;;;;YAC1B,OAAO;YACP,aAAa;QACf;QACA;YACE,oBAAM,8OAAC,sMAAA,CAAA,SAAM;gBAAC,WAAU;;;;;;YACxB,OAAO;YACP,aAAa;QACf;QACA;YACE,oBAAM,8OAAC,kNAAA,CAAA,YAAS;gBAAC,WAAU;;;;;;YAC3B,OAAO;YACP,aAAa;QACf;KACD;IAED,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAO,WAAU;0BAChB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAG,WAAU;kDAAqC;;;;;;;;;;;;;;;;0CAGvD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;kDACX;;;;;;kDAGD,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;kDACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAST,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;;kDACZ,8OAAC;wCAAK,WAAU;kDAAQ;;;;;;kDACxB,8OAAC;wCAAK,WAAU;kDAAwB;;;;;;;;;;;;0CAE1C,8OAAC;gCAAE,WAAU;0CAA2F;;;;;;0CAIxG,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;4CACH,MAAK;4CACL,WAAU;sDACX;;;;;;;;;;;kDAIH,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;4CACH,MAAK;4CACL,WAAU;sDACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAUX,8OAAC;gBAAQ,IAAG;gBAAW,WAAU;0BAC/B,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAwC;;;;;;8CAGtD,8OAAC;oCAAE,WAAU;8CAA6B;;;;;;;;;;;;sCAK5C,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;0CACZ,SAAS,GAAG,CAAC,CAAC,SAAS,sBACtB,8OAAC;wCAEC,WAAU;;0DAEV,8OAAC;0DACC,cAAA,8OAAC;oDAAK,WAAU;8DACb,QAAQ,IAAI;;;;;;;;;;;0DAGjB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAG,WAAU;kEACX,QAAQ,KAAK;;;;;;kEAEhB,8OAAC;wDAAE,WAAU;kEACV,QAAQ,WAAW;;;;;;;;;;;;;uCAbnB;;;;;;;;;;;;;;;;;;;;;;;;;;0BAwBjB,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;kDAAwC;;;;;;kDAGtD,8OAAC;wCAAE,WAAU;kDAA6B;;;;;;kDAI1C,8OAAC;wCAAG,WAAU;;0DACZ,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC,sMAAA,CAAA,SAAM;gEAAC,WAAU;;;;;;;;;;;;;;;;kEAGtB,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAG,WAAU;0EAA8C;;;;;;0EAG5D,8OAAC;gEAAG,WAAU;0EAA+B;;;;;;;;;;;;;;;;;;0DAMjD,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC,8MAAA,CAAA,aAAU;gEAAC,WAAU;;;;;;;;;;;;;;;;kEAG1B,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAG,WAAU;0EAA8C;;;;;;0EAG5D,8OAAC;gEAAG,WAAU;0EAA+B;;;;;;;;;;;;;;;;;;0DAMjD,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC,oMAAA,CAAA,QAAK;gEAAC,WAAU;;;;;;;;;;;;;;;;kEAGrB,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAG,WAAU;0EAA8C;;;;;;0EAG5D,8OAAC;gEAAG,WAAU;0EAA+B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAQrD,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAA2C;;;;;;sDAGzD,8OAAC;4CAAG,WAAU;sDACX;gDACC;gDACA;gDACA;gDACA;gDACA;gDACA;gDACA;gDACA;6CACD,CAAC,GAAG,CAAC,CAAC,MAAM,sBACX,8OAAC;oDAAe,WAAU;;sEACxB,8OAAC,2NAAA,CAAA,cAAW;4DAAC,WAAU;;;;;;sEACvB,8OAAC;4DAAK,WAAU;sEAAiB;;;;;;;mDAF1B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAavB,8OAAC;gBAAO,WAAU;0BAChB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAgC;;;;;;0CAC9C,8OAAC;gCAAE,WAAU;0CAAqB;;;;;;0CAGlC,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASf", "debugId": null}}]}