import { User<PERSON><PERSON>, EventStatus, AttendanceStatus, MessageType } from '@prisma/client'

export interface User {
  id: string
  email: string
  firstName: string
  lastName: string
  phone?: string
  role: UserRole
  avatar?: string
  isActive: boolean
  createdAt: Date
  updatedAt: Date
}

export interface UserProfile {
  id: string
  userId: string
  dateOfBirth?: Date
  address?: string
  allergies?: string
  medicalInfo?: string
  emergencyContact?: string
  preferences?: any
}

export interface Event {
  id: string
  title: string
  description?: string
  startDate: Date
  endDate: Date
  location?: string
  maxParticipants?: number
  status: EventStatus
  organizerId: string
  qrCode?: string
  createdAt: Date
  updatedAt: Date
}

export interface Activity {
  id: string
  title: string
  description?: string
  startTime: Date
  endTime: Date
  location?: string
  eventId: string
  groupId?: string
  documents?: any
  createdAt: Date
  updatedAt: Date
}

export interface Group {
  id: string
  name: string
  description?: string
  eventId: string
  color?: string
  createdAt: Date
}

export interface ChatRoom {
  id: string
  name: string
  description?: string
  type: string
  eventId?: string
  groupId?: string
  isActive: boolean
  createdAt: Date
}

export interface Message {
  id: string
  content: string
  type: MessageType
  senderId: string
  chatRoomId: string
  replyToId?: string
  attachments?: any
  isEdited: boolean
  createdAt: Date
  updatedAt: Date
  sender?: User
}

export interface AttendanceRecord {
  id: string
  userId: string
  eventId?: string
  activityId?: string
  status: AttendanceStatus
  checkInTime?: Date
  checkOutTime?: Date
  location?: string
  qrCodeUsed?: string
  createdAt: Date
}

export interface MediaAlbum {
  id: string
  title: string
  description?: string
  eventId: string
  isPublic: boolean
  createdAt: Date
}

export interface MediaFile {
  id: string
  filename: string
  originalName: string
  mimeType: string
  size: number
  url: string
  albumId: string
  uploadedBy: string
  createdAt: Date
}

export interface EmergencyContact {
  id: string
  userId: string
  name: string
  relationship: string
  phone: string
  email?: string
  isPrimary: boolean
}

// Types pour les formulaires
export interface CreateEventForm {
  title: string
  description?: string
  startDate: string
  endDate: string
  location?: string
  maxParticipants?: number
}

export interface CreateActivityForm {
  title: string
  description?: string
  startTime: string
  endTime: string
  location?: string
  groupId?: string
}

export interface UserRegistrationForm {
  email: string
  firstName: string
  lastName: string
  phone?: string
  dateOfBirth?: string
  address?: string
  allergies?: string
  medicalInfo?: string
}

// Types pour les réponses API
export interface ApiResponse<T> {
  success: boolean
  data?: T
  error?: string
  message?: string
}

export interface PaginatedResponse<T> {
  data: T[]
  total: number
  page: number
  limit: number
  totalPages: number
}

// Types pour les notifications
export interface Notification {
  id: string
  title: string
  message: string
  type: 'info' | 'success' | 'warning' | 'error'
  userId: string
  isRead: boolean
  createdAt: Date
}

// Types pour les statistiques
export interface EventStats {
  totalParticipants: number
  confirmedParticipants: number
  pendingParticipants: number
  totalActivities: number
  attendanceRate: number
}

export interface DashboardStats {
  totalEvents: number
  activeEvents: number
  totalParticipants: number
  totalMessages: number
  recentActivity: any[]
}
