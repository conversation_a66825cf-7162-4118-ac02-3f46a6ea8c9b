import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'
import { generateQRCode } from '@/lib/utils'

const createEventSchema = z.object({
  title: z.string().min(1, 'Le titre est requis'),
  description: z.string().optional(),
  startDate: z.string().datetime('Date de début invalide'),
  endDate: z.string().datetime('Date de fin invalide'),
  location: z.string().optional(),
  maxParticipants: z.number().positive().optional(),
  organizerId: z.string().min(1, 'L\'organisateur est requis')
})

const getEventsSchema = z.object({
  page: z.string().optional().default('1'),
  limit: z.string().optional().default('10'),
  status: z.enum(['DRAFT', 'PUBLISHED', 'ACTIVE', 'COMPLETED', 'CANCELLED']).optional(),
  organizerId: z.string().optional()
})

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const query = Object.fromEntries(searchParams.entries())
    
    const validatedQuery = getEventsSchema.parse(query)
    const page = parseInt(validatedQuery.page)
    const limit = parseInt(validatedQuery.limit)
    const skip = (page - 1) * limit
    
    // Construire les filtres
    const where: any = {}
    if (validatedQuery.status) {
      where.status = validatedQuery.status
    }
    if (validatedQuery.organizerId) {
      where.organizerId = validatedQuery.organizerId
    }
    
    // Récupérer les événements avec pagination
    const [events, total] = await Promise.all([
      prisma.event.findMany({
        where,
        skip,
        take: limit,
        include: {
          organizer: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              email: true
            }
          },
          participations: {
            select: {
              id: true,
              status: true,
              user: {
                select: {
                  id: true,
                  firstName: true,
                  lastName: true
                }
              }
            }
          },
          _count: {
            select: {
              participations: true,
              activities: true
            }
          }
        },
        orderBy: {
          createdAt: 'desc'
        }
      }),
      prisma.event.count({ where })
    ])
    
    return NextResponse.json({
      success: true,
      data: events,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit)
      }
    })
    
  } catch (error) {
    console.error('Erreur lors de la récupération des événements:', error)
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Paramètres invalides', details: error.errors },
        { status: 400 }
      )
    }
    
    return NextResponse.json(
      { error: 'Erreur interne du serveur' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    
    // Validation des données
    const validatedData = createEventSchema.parse(body)
    
    // Vérifier que la date de fin est après la date de début
    const startDate = new Date(validatedData.startDate)
    const endDate = new Date(validatedData.endDate)
    
    if (endDate <= startDate) {
      return NextResponse.json(
        { error: 'La date de fin doit être après la date de début' },
        { status: 400 }
      )
    }
    
    // Vérifier que l'organisateur existe
    const organizer = await prisma.user.findUnique({
      where: { id: validatedData.organizerId }
    })
    
    if (!organizer) {
      return NextResponse.json(
        { error: 'Organisateur non trouvé' },
        { status: 404 }
      )
    }
    
    // Générer un QR code unique pour l'événement
    const qrCode = generateQRCode(`event_${Date.now()}_${validatedData.organizerId}`)
    
    // Créer l'événement
    const event = await prisma.event.create({
      data: {
        title: validatedData.title,
        description: validatedData.description,
        startDate: new Date(validatedData.startDate),
        endDate: new Date(validatedData.endDate),
        location: validatedData.location,
        maxParticipants: validatedData.maxParticipants,
        organizerId: validatedData.organizerId,
        qrCode,
        status: 'DRAFT'
      },
      include: {
        organizer: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true
          }
        }
      }
    })
    
    return NextResponse.json({
      success: true,
      message: 'Événement créé avec succès',
      data: event
    }, { status: 201 })
    
  } catch (error) {
    console.error('Erreur lors de la création de l\'événement:', error)
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Données invalides', details: error.errors },
        { status: 400 }
      )
    }
    
    return NextResponse.json(
      { error: 'Erreur interne du serveur' },
      { status: 500 }
    )
  }
}
